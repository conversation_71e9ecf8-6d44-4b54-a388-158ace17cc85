/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.c
  * @brief   This file provides code for the configuration
  *          of all used GPIO pins.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"
#include <stdio.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/** Configure pins as
        * Analog
        * Input
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOC, LED1_Pin, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, CS_Pin|V_OUT_Pin, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, VCHK_Pin, GPIO_PIN_SET);

  /*Configure GPIO pin Output Level for power control pins */
  HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin|RF_PWR_Pin, GPIO_PIN_SET);  // 初始化为高电平（关闭电源）
  HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_SET);              // 初始化为高电平（关闭电源）

  /*Configure GPIO pins : LED1_Pin (CAM_PW_Pin单独配置) */
  GPIO_InitStruct.Pin = LED1_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

  /*Configure GPIO pins : CS_Pin V_OUT_Pin */
  GPIO_InitStruct.Pin = CS_Pin|V_OUT_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pin : VCHK_Pin */
  GPIO_InitStruct.Pin = VCHK_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pins : GPS_PWR_Pin RF_PWR_Pin - 硬件缺陷已修复，使用正常输出模式 */
  GPIO_InitStruct.Pin = GPS_PWR_Pin|RF_PWR_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /*Configure GPIO pin : PB5 */
  GPIO_InitStruct.Pin = GPIO_PIN_5;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_PULLDOWN;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /*Configure GPIO pin : CAM_PW_Pin - 硬件缺陷已修复，使用正常输出模式 */
  GPIO_InitStruct.Pin = CAM_PW_Pin;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

}

/* USER CODE BEGIN 2 */

/**
 * @brief RF电源引脚设为输出低电平（开启电源）
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void RF_PowerPin_SetOutputLow(void)
{
    // 直接设置输出电平为低（开启电源）
    HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_RESET);
}

/**
 * @brief RF电源引脚设为输出高电平（关闭电源）
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void RF_PowerPin_SetOutputHigh(void)
{
    // 直接设置输出电平为高（关闭电源）
    HAL_GPIO_WritePin(GPIOB, RF_PWR_Pin, GPIO_PIN_SET);
}

/**
 * @brief GPS电源引脚设为输出低电平（开启电源）
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void GPS_PowerPin_SetOutputLow(void)
{
    // 直接设置输出电平为低（开启电源）
    HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_RESET);
}

/**
 * @brief GPS电源引脚设为输出高电平（关闭电源）
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void GPS_PowerPin_SetOutputHigh(void)
{
    // 直接设置输出电平为高（关闭电源）
    HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_SET);
}

/**
 * @brief 摄像头电源引脚设为输出低电平（开启电源）
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void CAM_PowerPin_SetOutputLow(void)
{
    // 直接设置输出电平为低（开启电源）
    HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_RESET);
}

/**
 * @brief 摄像头电源引脚设为输出高电平（关闭电源）
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void CAM_PowerPin_SetOutputHigh(void)
{
    // 直接设置输出电平为高（关闭电源）
    HAL_GPIO_WritePin(GPIOC, CAM_PW_Pin, GPIO_PIN_SET);
}

/**
 * @brief 电源引脚初始化为输出模式（唤醒后调用）
 * @note 唤醒后调用，初始化为输出模式并关闭所有电源
 * @note 硬件缺陷已修复，使用正常输出模式控制
 */
void PowerPins_InitForWakeup(void)
{
    // 初始化电源引脚状态 - 全部关闭（高电平）
    RF_PowerPin_SetOutputHigh();   // RF模块电源关闭（输出高电平）
    GPS_PowerPin_SetOutputHigh();  // GPS模块电源关闭（输出高电平）
    CAM_PowerPin_SetOutputHigh();  // 摄像头模块电源关闭（输出高电平）
    VCHK_ON;                       // AD采样开关打开，唤醒后需要进行电压检测
}

/**
 * @brief 检查CAM_PW引脚状态（调试用）
 * @note 硬件缺陷已修复，现在使用正常输出模式控制
 */
void CAM_PowerPin_CheckStatus(void)
{
    // 读取引脚配置
    GPIO_TypeDef* port = GPIOC;
    uint32_t pin_pos = 15;  // CAM_PW_Pin is GPIO_PIN_15

    // 检查MODER寄存器 (00=输入, 01=输出, 10=复用, 11=模拟)
    uint32_t mode = (port->MODER >> (pin_pos * 2)) & 0x3;

    // 检查引脚电平
    uint32_t pin_state = HAL_GPIO_ReadPin(GPIOC, CAM_PW_Pin);

    printf("CAM_PW Status: Mode=%s, Level=%s (HIGH=OFF, LOW=ON)\r\n",
           (mode == 0) ? "INPUT" : (mode == 1) ? "OUTPUT" : "OTHER",
           (pin_state == GPIO_PIN_SET) ? "HIGH" : "LOW");
}

/* USER CODE END 2 */
